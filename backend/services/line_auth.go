package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"

	"gorm.io/gorm"
)

type LineAuthService struct {
	db *gorm.DB
}

func NewLineAuthService(db *gorm.DB) *LineAuthService {
	return &LineAuthService{db: db}
}

type LineTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
	IDToken      string `json:"id_token"`
}

type LineProfile struct {
	UserID        string `json:"userId"`
	DisplayName   string `json:"displayName"`
	PictureURL    string `json:"pictureUrl"`
	StatusMessage string `json:"statusMessage"`
}

func (s *LineAuthService) GetAuthURL() string {
	channelID := os.Getenv("LINE_CHANNEL_ID")
	callbackURL := os.Getenv("LINE_CALLBACK_URL")
	state := "random_state" // In production, use a secure random string

	params := url.Values{}
	params.Set("response_type", "code")
	params.Set("client_id", channelID)
	params.Set("redirect_uri", callbackURL)
	params.Set("scope", "profile openid")
	params.Set("state", state)

	return fmt.Sprintf("https://access.line.me/oauth2/v2.1/authorize?%s", params.Encode())
}

func (s *LineAuthService) ExchangeCodeForToken(code string) (*LineTokenResponse, error) {
	channelID := os.Getenv("LINE_CHANNEL_ID")
	channelSecret := os.Getenv("LINE_CHANNEL_SECRET")
	callbackURL := os.Getenv("LINE_CALLBACK_URL")

	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)
	data.Set("redirect_uri", callbackURL)
	data.Set("client_id", channelID)
	data.Set("client_secret", channelSecret)

	resp, err := http.PostForm("https://api.line.me/oauth2/v2.1/token", data)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var tokenResp LineTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return nil, err
	}

	return &tokenResp, nil
}

func (s *LineAuthService) GetUserProfile(accessToken string) (*LineProfile, error) {
	req, err := http.NewRequest("GET", "https://api.line.me/v2/profile", nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var profile LineProfile
	if err := json.Unmarshal(body, &profile); err != nil {
		return nil, err
	}

	return &profile, nil
}

// func (s *LineAuthService) SaveOrUpdateUser(profile *LineProfile, tokenResp *LineTokenResponse) error {
// 	user := models.User{
// 		LineID:        profile.UserID,
// 		DisplayName:   profile.DisplayName,
// 		PictureURL:    profile.PictureURL,
// 		StatusMessage: profile.StatusMessage,
// 		AccessToken:   tokenResp.AccessToken,
// 		RefreshToken:  tokenResp.RefreshToken,
// 		TokenExpiry:   time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second),
// 	}

// 	result := s.db.Where("line_id = ?", profile.UserID).Updates(&user)
// 	if result.RowsAffected == 0 {
// 		return s.db.Create(&user).Error
// 	}
// 	return result.Error
// }
