apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: line
  labels:
    app: line-oauth2-backend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: line-oauth2-backend

---
apiVersion: v1
kind: Service
metadata:
  name: backend-nodeport
  namespace: line
  labels:
    app: line-oauth2-backend
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 8080
    nodePort: 30080
    protocol: TCP
    name: http
  selector:
    app: line-oauth2-backend
