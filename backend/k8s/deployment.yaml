apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  namespace: line
  labels:
    app: line-oauth2-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: line-oauth2-backend
  template:
    metadata:
      labels:
        app: line-oauth2-backend
    spec:
      containers:
      - name: backend
        image: line-oauth2-backend:latest  # Replace with your actual image
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: PORT
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: DATABASE_URL
        - name: LINE_CHANNEL_ID
          valueFrom:
            secretKeyRef:
              name: backend-secret
              key: LINE_CHANNEL_ID
        - name: LINE_CHANNEL_SECRET
          valueFrom:
            secretKeyRef:
              name: backend-secret
              key: LINE_CHANNEL_SECRET
        - name: LINE_CALLBACK_URL
          valueFrom:
            secretKeyRef:
              name: backend-secret
              key: LINE_CALLBACK_URL
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
      restartPolicy: Always
