# Kubernetes Deployment for Line OAuth2 Backend

This directory contains Kubernetes manifests to deploy the Line OAuth2 Backend application to a Kubernetes cluster.

## Prerequisites

- Kubernetes cluster with access configured
- `kubectl` installed and configured
- Docker image built and available in your registry
- PostgreSQL database running in the `default` namespace

## Quick Start

### 1. Update Configuration

Before deploying, update the following files with your actual values:

**`secret.yaml`** - Update with your LINE credentials:
```bash
# Encode your values
echo -n "your-line-channel-id" | base64
echo -n "your-line-channel-secret" | base64
echo -n "https://your-domain.com/api/auth/callback" | base64
```

**`deployment.yaml`** - Update the image name:
```yaml
image: your-registry/line-oauth2-backend:latest
```

**`ingress.yaml`** - Update the host:
```yaml
host: your-domain.com
```

### 2. Deploy

Using the deployment script:
```bash
./deploy.sh
```

Or manually:
```bash
kubectl apply -f .
```

### 3. Verify Deployment

```bash
kubectl get pods -n line
kubectl get services -n line
kubectl logs -n line -l app=line-oauth2-backend
```

## Files Description

| File | Description |
|------|-------------|
| `namespace.yaml` | Creates the `line` namespace |
| `configmap.yaml` | Non-sensitive configuration (PORT, DATABASE_URL) |
| `secret.yaml` | Sensitive data (LINE credentials) |
| `deployment.yaml` | Main application deployment with health checks |
| `service.yaml` | ClusterIP and NodePort services |
| `ingress.yaml` | Ingress for external access |
| `hpa.yaml` | Horizontal Pod Autoscaler |
| `networkpolicy.yaml` | Network security policies |
| `kustomization.yaml` | Kustomize configuration |
| `deploy.sh` | Deployment script |

## Configuration

### Database Connection

The application connects to your existing PostgreSQL database:
- **Host**: `postgres.default.svc.cluster.local:5432`
- **Database**: `ps_db`
- **User**: `ps_user`
- **Password**: `SecurePassword`

### Environment Variables

| Variable | Source | Description |
|----------|--------|-------------|
| `PORT` | ConfigMap | Application port (8080) |
| `DATABASE_URL` | ConfigMap | PostgreSQL connection string |
| `LINE_CHANNEL_ID` | Secret | LINE Channel ID |
| `LINE_CHANNEL_SECRET` | Secret | LINE Channel Secret |
| `LINE_CALLBACK_URL` | Secret | OAuth callback URL |

## Health Checks

The deployment includes comprehensive health checks:

- **Liveness Probe**: `/health/live` - Checks if the app is alive
- **Readiness Probe**: `/health/ready` - Checks if the app is ready to serve traffic
- **Startup Probe**: `/health/live` - Gives the app time to start up

## Scaling

### Manual Scaling
```bash
kubectl scale deployment backend-deployment -n line --replicas=5
```

### Auto Scaling
The HPA automatically scales based on:
- CPU usage (target: 70%)
- Memory usage (target: 80%)
- Min replicas: 2
- Max replicas: 10

## Access Methods

### 1. Port Forward (Development)
```bash
kubectl port-forward -n line service/backend-service 8080:80
# Access at http://localhost:8080
```

### 2. NodePort (Testing)
```bash
kubectl get service backend-nodeport -n line
# Access at http://<node-ip>:30080
```

### 3. Ingress (Production)
Configure your DNS to point to the ingress controller IP.

## Monitoring

### Check Pod Status
```bash
kubectl get pods -n line -w
```

### View Logs
```bash
kubectl logs -n line -l app=line-oauth2-backend -f
```

### Check Health Endpoints
```bash
kubectl port-forward -n line service/backend-service 8080:80
curl http://localhost:8080/health
curl http://localhost:8080/health/ready
curl http://localhost:8080/health/live
```

## Troubleshooting

### Common Issues

1. **Image Pull Errors**
   - Ensure the image exists in your registry
   - Check image pull secrets if using private registry

2. **Database Connection Issues**
   - Verify PostgreSQL is running in default namespace
   - Check network policies allow cross-namespace communication

3. **Health Check Failures**
   - Check application logs
   - Verify health endpoints are responding

### Useful Commands

```bash
# Check deployment status
kubectl rollout status deployment/backend-deployment -n line

# Describe pod for detailed info
kubectl describe pod -n line -l app=line-oauth2-backend

# Check events
kubectl get events -n line --sort-by='.lastTimestamp'

# Delete deployment
./deploy.sh --delete
```

## Security

- Runs as non-root user
- Network policies restrict traffic
- Secrets are base64 encoded (consider using external secret management)
- Resource limits prevent resource exhaustion

## Production Considerations

1. **Use external secret management** (e.g., Kubernetes secrets, Vault)
2. **Configure TLS** in the ingress
3. **Set up monitoring** (Prometheus, Grafana)
4. **Configure log aggregation** (ELK stack, Fluentd)
5. **Use private container registry**
6. **Implement backup strategies** for persistent data
