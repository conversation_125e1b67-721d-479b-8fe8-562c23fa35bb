apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backend-ingress
  namespace: line
  labels:
    app: line-oauth2-backend
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Accept, Authorization, Content-Type"
    nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  ingressClassName: nginx  # Adjust based on your ingress controller
  rules:
  - host: line-oauth2-backend.local  # Replace with your domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 80
  # Uncomment and configure for HTTPS
  # tls:
  # - hosts:
  #   - line-oauth2-backend.local
  #   secretName: backend-tls
