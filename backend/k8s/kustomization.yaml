apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: line-oauth2-backend
  namespace: line

resources:
- namespace.yaml
- configmap.yaml
- secret.yaml
- deployment.yaml
- service.yaml
- ingress.yaml
- hpa.yaml
- networkpolicy.yaml

commonLabels:
  app: line-oauth2-backend
  version: v1.0.0

images:
- name: line-oauth2-backend
  newTag: latest

# Uncomment to add resource limits across all resources
# patchesStrategicMerge:
# - resource-limits.yaml
