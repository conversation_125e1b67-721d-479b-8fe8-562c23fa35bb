#!/bin/bash

# Kubernetes deployment script for Line OAuth2 Backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

# Default values
NAMESPACE="line"
IMAGE_TAG="latest"
DRY_RUN=false
DELETE=false

# Function to show usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -n, --namespace NAME    Kubernetes namespace (default: $NAMESPACE)"
    echo "  -t, --tag TAG          Docker image tag (default: $TAG)"
    echo "  -d, --dry-run          Show what would be deployed without applying"
    echo "  -r, --delete           Delete the deployment"
    echo "  -h, --help             Show this help message"
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -r|--delete)
            DELETE=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            ;;
    esac
done

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to the cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

print_header "Line OAuth2 Backend Kubernetes Deployment"
print_status "Namespace: $NAMESPACE"
print_status "Image Tag: $IMAGE_TAG"

if [[ "$DELETE" == "true" ]]; then
    print_warning "Deleting deployment..."
    kubectl delete -f . --ignore-not-found=true
    print_status "Deployment deleted successfully!"
    exit 0
fi

if [[ "$DRY_RUN" == "true" ]]; then
    print_status "Dry run mode - showing what would be deployed:"
    kubectl apply -f . --dry-run=client
    exit 0
fi

# Deploy the application
print_status "Deploying to Kubernetes..."

# Apply manifests in order
print_status "Creating namespace..."
kubectl apply -f namespace.yaml

print_status "Creating ConfigMap..."
kubectl apply -f configmap.yaml

print_status "Creating Secret..."
kubectl apply -f secret.yaml

print_status "Creating Deployment..."
kubectl apply -f deployment.yaml

print_status "Creating Service..."
kubectl apply -f service.yaml

print_status "Creating Ingress..."
kubectl apply -f ingress.yaml

print_status "Creating HPA..."
kubectl apply -f hpa.yaml

print_status "Creating Network Policy..."
kubectl apply -f networkpolicy.yaml

print_status "Deployment completed successfully!"

# Show deployment status
print_header "Deployment Status"
kubectl get pods -n $NAMESPACE -l app=line-oauth2-backend
kubectl get services -n $NAMESPACE -l app=line-oauth2-backend

print_status "To check logs: kubectl logs -n $NAMESPACE -l app=line-oauth2-backend"
print_status "To port-forward: kubectl port-forward -n $NAMESPACE service/backend-service 8080:80"
