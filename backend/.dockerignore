# Binaries
*.exe
*.exe~
*.dll
*.so
*.dylib
main

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Environment files (will be provided via environment variables or mounted)
.env
.env.local
.env.*.local

# Documentation
*.md
README.md

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore
