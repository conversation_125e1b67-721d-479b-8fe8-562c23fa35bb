docker build -t rubyhcm/frontend-line:latest .

docker push rubyhcm/frontend-line:latest

kubectl apply -f k8s/
--------
kubectl get pods -n line

kubectl get services -n line

kubectl describe service frontend-service -n line

kubectl port-forward service/frontend-service 30001:80 -n line

minikube service frontend-service -n=line

i have postgres from other name space: postgres.default.svc.cluster.local:5432
  POSTGRES_DB: ps_db
  POSTGRES_USER: ps_user
  POSTGRES_PASSWORD: SecurePassword
how can i connect 

