#!/bin/bash

# Build script for Line OAuth2 Backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="line-oauth2-backend"
TAG="latest"
DOCKERFILE="Dockerfile"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -n, --name NAME     Docker image name (default: $IMAGE_NAME)"
    echo "  -t, --tag TAG       Docker image tag (default: $TAG)"
    echo "  -p, --prod          Use production Dockerfile"
    echo "  -r, --run           Run the container after building"
    echo "  -c, --compose       Use docker-compose to build and run"
    echo "  -h, --help          Show this help message"
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--prod)
            DOCKERFILE="Dockerfile.prod"
            shift
            ;;
        -r|--run)
            RUN_CONTAINER=true
            shift
            ;;
        -c|--compose)
            USE_COMPOSE=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            ;;
    esac
done

# Main execution
if [[ "$USE_COMPOSE" == "true" ]]; then
    print_status "Building and running with docker-compose..."
    docker-compose up --build
else
    print_status "Building Docker image: $IMAGE_NAME:$TAG"
    print_status "Using Dockerfile: $DOCKERFILE"
    
    # Build the image
    docker build -f "$DOCKERFILE" -t "$IMAGE_NAME:$TAG" .
    
    if [[ $? -eq 0 ]]; then
        print_status "Build completed successfully!"
        
        if [[ "$RUN_CONTAINER" == "true" ]]; then
            print_status "Running container..."
            docker run -p 8080:8080 --env-file .env "$IMAGE_NAME:$TAG"
        fi
    else
        print_error "Build failed!"
        exit 1
    fi
fi
