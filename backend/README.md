# Line OAuth2 Backend

A Go backend service for LINE OAuth2 authentication with health checks and Docker support.

## Features

- LINE OAuth2 authentication
- Health check endpoints (`/health`, `/health/ready`, `/health/live`)
- PostgreSQL database integration
- Docker containerization
- Production-ready configuration

## Quick Start

### Using Docker Compose (Recommended)

1. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your LINE credentials
   ```

2. **Run with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

   This will start both the backend service and PostgreSQL database.

### Using Docker

1. **Build the image:**
   ```bash
   docker build -t line-oauth2-backend .
   ```

2. **Run the container:**
   ```bash
   docker run -p 8080:8080 --env-file .env line-oauth2-backend
   ```

### Using the Build Script

The included `build.sh` script provides convenient options:

```bash
# Build and run with docker-compose
./build.sh --compose

# Build Docker image
./build.sh --name my-app --tag v1.0.0

# Build production image
./build.sh --prod

# Build and run container
./build.sh --run
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LINE_CHANNEL_ID` | LINE Channel ID | Required |
| `LINE_CHANNEL_SECRET` | LINE Channel Secret | Required |
| `LINE_CALLBACK_URL` | OAuth callback URL | Required |
| `DATABASE_URL` | PostgreSQL connection string | `postgres://postgres:postgres@localhost:5432/line_oauth2` |
| `PORT` | Server port | `8080` |

## API Endpoints

### Authentication
- `GET /api/auth/line` - Get LINE login URL
- `GET /api/auth/callback` - Handle OAuth callback

### Health Checks
- `GET /health` - General health check
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe

## Docker Files

- **`Dockerfile`** - Standard multi-stage build with Alpine Linux
- **`Dockerfile.prod`** - Production build with distroless base image
- **`docker-compose.yml`** - Complete stack with database
- **`.dockerignore`** - Optimized build context

## Development

### Local Development
```bash
go mod download
go run main.go
```

### Testing Health Endpoints
```bash
curl http://localhost:8080/health
curl http://localhost:8080/health/ready
curl http://localhost:8080/health/live
```

## Production Deployment

For production, use the distroless image:

```bash
docker build -f Dockerfile.prod -t line-oauth2-backend:prod .
```

## Health Checks

The application includes comprehensive health checks:

- **Liveness**: Basic application responsiveness
- **Readiness**: Database connectivity and service readiness
- **Health**: Overall application health with detailed service status

These are compatible with Kubernetes probes and load balancer health checks.
