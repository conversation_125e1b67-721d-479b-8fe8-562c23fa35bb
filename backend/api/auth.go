package api

import (
	"encoding/json"
	"net/http"

	"line_oauth2/services"
)

type AuthHandler struct {
	lineAuth *services.LineAuthService
}

func NewAuthHandler(lineAuth *services.LineAuthService) *AuthHandler {
	return &AuthHandler{lineAuth: lineAuth}
}

func (h *AuthHandler) GetLoginURL(w http.ResponseWriter, r *http.Request) {
	authURL := h.lineAuth.GetAuthURL()
	json.NewEncoder(w).Encode(map[string]string{"url": authURL})
}

func (h *AuthHandler) HandleCallback(w http.ResponseWriter, r *http.Request) {
	code := r.URL.Query().Get("code")
	if code == "" {
		http.Error(w, "Code is required", http.StatusBadRequest)
		return
	}

	// Exchange code for token
	tokenResp, err := h.lineAuth.ExchangeCodeForToken(code)
	if err != nil {
		http.Error(w, "Failed to exchange code for token", http.StatusInternalServerError)
		return
	}

	// Get user profile
	profile, err := h.lineAuth.GetUserProfile(tokenResp.AccessToken)
	if err != nil {
		http.Error(w, "Failed to get user profile", http.StatusInternalServerError)
		return
	}

	// Save or update user
	// if err := h.lineAuth.SaveOrUpdateUser(profile, tokenResp); err != nil {
	// 	http.Error(w, "Failed to save user", http.StatusInternalServerError)
	// 	return
	// }

	// Return user info
	json.NewEncoder(w).Encode(map[string]interface{}{
		"user": profile,
	})

	// Return user info
	// json.NewEncoder(w).Encode(map[string]interface{}{
	// 	"user":  profile,
	// 	"token": tokenResp,
	// })
}
