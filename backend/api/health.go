package api

import (
	"encoding/json"
	"net/http"
	"time"

	"line_oauth2/config"
)

type HealthResponse struct {
	Status    string            `json:"status"`
	Timestamp string            `json:"timestamp"`
	Version   string            `json:"version"`
	Services  map[string]string `json:"services"`
}

type HealthHandler struct{}

func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

func (h *HealthHandler) CheckHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// Check database connection
	dbStatus := "healthy"
	if config.DB != nil {
		sqlDB, err := config.DB.DB()
		if err != nil || sqlDB.Ping() != nil {
			dbStatus = "unhealthy"
		}
	} else {
		dbStatus = "unhealthy"
	}
	
	// Determine overall status
	overallStatus := "healthy"
	if dbStatus == "unhealthy" {
		overallStatus = "unhealthy"
		w.WriteHeader(http.StatusServiceUnavailable)
	}
	
	response := HealthResponse{
		Status:    overallStatus,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
		Version:   "1.0.0", // You can make this dynamic if needed
		Services: map[string]string{
			"database": dbStatus,
		},
	}
	
	json.NewEncoder(w).Encode(response)
}

func (h *HealthHandler) CheckReadiness(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// Check if all critical services are ready
	ready := true
	services := make(map[string]string)
	
	// Check database
	if config.DB != nil {
		sqlDB, err := config.DB.DB()
		if err != nil || sqlDB.Ping() != nil {
			ready = false
			services["database"] = "not ready"
		} else {
			services["database"] = "ready"
		}
	} else {
		ready = false
		services["database"] = "not ready"
	}
	
	status := "ready"
	if !ready {
		status = "not ready"
		w.WriteHeader(http.StatusServiceUnavailable)
	}
	
	response := map[string]interface{}{
		"status":    status,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"services":  services,
	}
	
	json.NewEncoder(w).Encode(response)
}

func (h *HealthHandler) CheckLiveness(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// Simple liveness check - if we can respond, we're alive
	response := map[string]interface{}{
		"status":    "alive",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}
	
	json.NewEncoder(w).Encode(response)
}
